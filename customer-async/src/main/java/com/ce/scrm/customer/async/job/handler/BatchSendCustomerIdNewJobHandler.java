package com.ce.scrm.customer.async.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.customer.async.job.entity.BatchSendCustomerIdDto;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.support.mq.RocketMqOperate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BatchSendCustomerIdNewJobHandler {

    /**
     * 查询客户id 开始的值
     */
    private long customerStartId = 0;

    /**
     * 查询size
     */
    private int querySize = 2000;

    /**
     * 批量发送customerId 数量
     */
    private int sendSize = 200;

    @Resource
    private CustomerService customerService;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_BATCH_SEND_CUSTOMER_ID_SDR_JOB_HANDLER)
    public ReturnT<String> batchSendCustomerIdJobHandler(String param) {

        log.info("batch send customer id job handler start");
        startTask(param);
        log.info("batch send customer id job handler end");
        return ReturnT.SUCCESS;

    }

    private synchronized void startTask(String param) {

        customerStartId = 0;

        // 针对参数处理
        if(StringUtils.isNotBlank(param)) {
            BatchSendCustomerIdDto batchSendCustomerIdDto = JSON.parseObject(param, BatchSendCustomerIdDto.class);
            // 发送指定customerIdList
            if (CollectionUtil.isNotEmpty(batchSendCustomerIdDto.getCustomerIdList())) {
                sendCustomerId(batchSendCustomerIdDto.getCustomerIdList());
                return;
            }
            // 断点开始
            customerStartId = batchSendCustomerIdDto.getCustomerStartId() != null ? batchSendCustomerIdDto.getCustomerStartId() : 0;
        }

        do {
            List<Customer> customerList = queryCustomerIdList();
            sendCustomerId(customerList.stream().map(Customer::getCustomerId).collect(Collectors.toList()));

            // 移动开始id
            if (CollectionUtil.isEmpty(customerList)) {
                break;
            }
            customerStartId = customerList.get(customerList.size() - 1).getId();
        } while (true);


    }

    private List<Customer> queryCustomerIdList() {
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                Customer::getId,
                Customer::getCustomerId
        );
        queryWrapper.gt(Customer::getId, customerStartId);
        queryWrapper.in(Customer::getProtectStatus, Arrays.asList("1", "2","3","5"));
        queryWrapper.last("limit " + querySize);
        return customerService.list(queryWrapper);
    }

    private void sendCustomerId(List<String> customerIdList) {
        if (CollectionUtil.isEmpty(customerIdList)) {
            return;
        }

        final AtomicInteger counter = new AtomicInteger();
        customerIdList.stream()
                .collect(Collectors.groupingBy(it -> counter.getAndIncrement() / sendSize))
                .values()
                .forEach(customerIds -> rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.SCRM_CUSTOMER_ID_BATCH_SEND_SDR_TOPIC, customerIds));

    }

}
