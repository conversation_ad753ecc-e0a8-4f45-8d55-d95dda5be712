package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import com.ce.scrm.customer.async.mq.entity.CmCustProtect;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.third.entity.view.CmCustVisitLogThirdView;
import com.ce.scrm.customer.service.third.invoke.CmCustVisitLogThirdService;
import com.ce.scrm.customer.service.third.invoke.ProtectThirdService;
import com.ce.scrm.extend.dubbo.entity.response.CmCustVisitLogRes;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: sdr需求上线之后，处理customer表的历史数据，只跑一次
 * @Author: 李金澎
 * @Date: 2020/12/23
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_CUSTOMER_ID_BATCH_SEND_SDR_TOPIC
        , consumerGroup = ServiceConstant.MqConstant.Group.CUSTOMER_HISTORY_DATA_GROUP
        , consumeThreadMax = 3)
public class CustomerHistoryConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private CustomerService customerService;

    @Resource
    private ProtectThirdService protectThirdService;

    @Resource
    private CmCustVisitLogThirdService cmCustVisitLogThirdService;

    @Override
    public void onMessage(MessageExt messageExt) {
//        if (1==1) {
//            return;
//        }
        try {
            log.info("计算customer历史数据，start");
            byte[] body = messageExt.getBody();
            List<String> customerIdList = byteArrayToJsonStringList(body);
            log.info("计算customer历史数据,customerIdList={}", JSON.toJSONString(customerIdList));
            handleData(customerIdList);
            log.info("计算customer历史数据，end");
        }catch (Exception e){
            log.error("计算customer历史数据 失败,",e);
        }
    }

    private void handleData(List<String> customerIdList) {
        if (CollectionUtil.isEmpty(customerIdList)) {
            return;
        }

        for (String customerId : customerIdList) {
            if (StringUtils.isBlank(customerId)) {
                return;
            }
            try {
                LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerId);

                updateBuildData(customerId,updateChainWrapper);

                updateChainWrapper.update();
                customerCacheHandler.del(customerId);
            }catch (Exception e) {
                log.error("计算customer历史数据 错误,customerId={}",customerId,e);
            }
        }

    }

    private void updateBuildData(String customerId,LambdaUpdateChainWrapper<Customer> updateChainWrapper) {

        CustomerProtectDubboVew newCmCustProtect = protectThirdService.getProtectByCustomerId(customerId);
        updateChainWrapper.set(Customer::getOperator,"1025");

        // SDR跟进次数(排除地理打卡) 不处理
//        updateChainWrapper.set(Customer::getSdrFollowCount, 0);
        // SDR跟进时间(排除地理打卡) 不处理
//        updateChainWrapper.set(Customer::getLastSdrFollowTime, null);
        // 跟进次数（手动录入）
        DubboPageInfo<CmCustVisitLogRes> lastOnePageByCondition1 = cmCustVisitLogThirdService.getLastOnePageByCondition(customerId, "DICT_VISIT_RECORDWAY_01");
        updateChainWrapper.set(Customer::getFollowCount, lastOnePageByCondition1.getTotal());
        // 上门次数（地理打卡）
        DubboPageInfo<CmCustVisitLogRes> lastOnePageByCondition2 = cmCustVisitLogThirdService.getLastOnePageByCondition(customerId, "DICT_VISIT_RECORDWAY_02");
        updateChainWrapper.set(Customer::getSiteCount, lastOnePageByCondition2.getTotal());
        CmCustVisitLogRes visitLogThirdView1 = CollectionUtil.isEmpty(lastOnePageByCondition1.getList()) ? null : lastOnePageByCondition1.getList().get(0);
        if (visitLogThirdView1 != null) {
            updateChainWrapper.set(Customer::getLastVisitType, visitLogThirdView1.getVisitType());
            updateChainWrapper.set(Customer::getLastFollowTime, visitLogThirdView1.getCreateTime());
        }
        CmCustVisitLogRes visitLogThirdView2 = CollectionUtil.isEmpty(lastOnePageByCondition2.getList()) ? null : lastOnePageByCondition1.getList().get(0);
        if (visitLogThirdView2 != null) {
            updateChainWrapper.set(Customer::getLastSiteTime, visitLogThirdView2.getCreateTime());
        }


        if (newCmCustProtect != null) {
            updateChainWrapper.set(Customer::getProtectStatus, newCmCustProtect.getStatus() == null ? null : String.valueOf(newCmCustProtect.getStatus()));
            updateChainWrapper.set(Customer::getProtectCustType, newCmCustProtect.getCustType() == null ? null : String.valueOf(newCmCustProtect.getCustType()));
            updateChainWrapper.set(Customer::getBindFlag, newCmCustProtect.getBindFlag());
            updateChainWrapper.set(Customer::getBusinessOpportunityConfirmationFlag, newCmCustProtect.getBusinessOpportunityConfirmationFlag());

            if (Objects.equals(newCmCustProtect.getStatus(),1)) {
                // 保护中
                updateChainWrapper.set(Customer::getProtectSalerId, newCmCustProtect.getSalerId());
                updateChainWrapper.set(Customer::getProtectBussdeptId, newCmCustProtect.getBussdeptId());
                updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
                updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
                updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

                updateChainWrapper.set(Customer::getProtectProtectTime, newCmCustProtect.getProtectTime());
                updateChainWrapper.set(Customer::getProtectProtectendTime, newCmCustProtect.getExceedTime());
            } else if (Objects.equals(newCmCustProtect.getStatus(),2)) {
                // 总监待分配
                updateChainWrapper.set(Customer::getProtectSalerId,null);
                updateChainWrapper.set(Customer::getProtectBussdeptId,null);
                updateChainWrapper.set(Customer::getProtectBuId,null);
                updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
                updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

                updateChainWrapper.set(Customer::getProtectProtectTime,null);
            } else if (Objects.equals(newCmCustProtect.getStatus(),3)) {
                // 部门经理待分配
                updateChainWrapper.set(Customer::getProtectSalerId,null);
                updateChainWrapper.set(Customer::getProtectBussdeptId, newCmCustProtect.getBussdeptId());
                updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
                updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
                updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

                updateChainWrapper.set(Customer::getProtectProtectTime,null);
            } else if (Objects.equals(newCmCustProtect.getStatus(),4)) {
                // 客户池
                updateChainWrapper.set(Customer::getProtectSalerId,null);
                updateChainWrapper.set(Customer::getProtectBussdeptId,null);
                updateChainWrapper.set(Customer::getProtectBuId,null);
                updateChainWrapper.set(Customer::getProtectSubcompanyId,null);
                updateChainWrapper.set(Customer::getProtectAreaId,null);

                updateChainWrapper.set(Customer::getProtectProtectTime,null);
            } else if (Objects.equals(newCmCustProtect.getStatus(),5)) {
                // 事业部总监待分配
                updateChainWrapper.set(Customer::getProtectSalerId,null);
                updateChainWrapper.set(Customer::getProtectBussdeptId,null);
                updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
                updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
                updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

                updateChainWrapper.set(Customer::getProtectProtectTime,null);
            }
        }



    }

    public static List<String> byteArrayToJsonStringList(byte[] body) {
        if (body == null || body.length == 0) {
            return new ArrayList<>();
        }

        try {
            String json = new String(body, StandardCharsets.UTF_8);
            return JSON.parseObject(json, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            throw new RuntimeException("JSON解析失败: " + e.getMessage(), e);
        }
    }

    private void updateStart(JSONObject beforeJson, JSONObject afterJson) {
        CmCustProtect oldCmCustProtect = JSONObject.parseObject(beforeJson.toJSONString(), CmCustProtect.class);
        CmCustProtect newCmCustProtect = JSONObject.parseObject(afterJson.toJSONString(), CmCustProtect.class);
        String customerId = newCmCustProtect.getCustId();
        if (StringUtils.isBlank(customerId)) {
            log.info("客户id不能为空");
            return;
        }

        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerId);

        basics(updateChainWrapper, newCmCustProtect, oldCmCustProtect);

        updateChainWrapper.update();
        customerCacheHandler.del(customerId);

    }

    private void basics(LambdaUpdateChainWrapper<Customer> updateChainWrapper, CmCustProtect newCmCustProtect, CmCustProtect oldCmCustProtect) {
        updateChainWrapper.set(Customer::getProtectStatus, newCmCustProtect.getStatus() == null ? null : String.valueOf(newCmCustProtect.getStatus()));
        updateChainWrapper.set(Customer::getProtectCustType, newCmCustProtect.getCustType() == null ? null : String.valueOf(newCmCustProtect.getCustType()));
        updateChainWrapper.set(Customer::getOperator,"1025");
        updateChainWrapper.set(Customer::getBindFlag, newCmCustProtect.getBindFlag());
        updateChainWrapper.set(Customer::getBusinessOpportunityConfirmationFlag, newCmCustProtect.getBusinessOpportunityConfirmationFlag());

        // 保护关系变化
        boolean b = oldCmCustProtect.getStatus()!=null && oldCmCustProtect.getStatus()==1  && newCmCustProtect.getStatus()!=1;
        if (b || !Objects.equals(oldCmCustProtect.getSalerId(), newCmCustProtect.getSalerId())) {
            updateChainWrapper.set(Customer::getSdrFollowCount,0);
            updateChainWrapper.set(Customer::getLastSdrFollowTime,null);
//            updateChainWrapper.set(Customer::getDistributeChannel,null);
//            updateChainWrapper.set(Customer::getDistributeTime,null);
//            updateChainWrapper.set(Customer::getReceiptEndTime,null);
            updateChainWrapper.set(Customer::getReviewStatus,null);
        }

        if (Objects.equals(newCmCustProtect.getStatus(),1)) {
            // 保护中
            updateChainWrapper.set(Customer::getProtectSalerId, newCmCustProtect.getSalerId());
            updateChainWrapper.set(Customer::getProtectBussdeptId, newCmCustProtect.getBussdeptId());
            updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime, newCmCustProtect.getProtectTime());
            updateChainWrapper.set(Customer::getProtectProtectendTime, newCmCustProtect.getExceedTime());
        } else if (Objects.equals(newCmCustProtect.getStatus(),2)) {
            // 总监待分配
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId,null);
            updateChainWrapper.set(Customer::getProtectBuId,null);
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        } else if (Objects.equals(newCmCustProtect.getStatus(),3)) {
            // 部门经理待分配
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId, newCmCustProtect.getBussdeptId());
            updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        } else if (Objects.equals(newCmCustProtect.getStatus(),4)) {
            // 客户池
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId,null);
            updateChainWrapper.set(Customer::getProtectBuId,null);
            updateChainWrapper.set(Customer::getProtectSubcompanyId,null);
            updateChainWrapper.set(Customer::getProtectAreaId,null);

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        } else if (Objects.equals(newCmCustProtect.getStatus(),5)) {
            // 事业部总监待分配
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId,null);
            updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        }
    }

    private void insertStart(JSONObject afterJson) {
        CmCustProtect newCmCustProtect = JSONObject.parseObject(afterJson.toJSONString(), CmCustProtect.class);
        String customerId = newCmCustProtect.getCustId();
        if (StringUtils.isBlank(customerId)) {
            log.info("客户id不能为空");
            return;
        }

        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerId);

        basics(updateChainWrapper, newCmCustProtect,new CmCustProtect());

        updateChainWrapper.update();
        customerCacheHandler.del(customerId);
    }
}