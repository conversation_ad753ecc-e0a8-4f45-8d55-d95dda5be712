package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户表
 * @TableName customer
 */
@TableName(value ="customer")
public class Customer implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 源数据ID
     */
    @TableField(value = "source_data_id")
    private String sourceDataId;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    @TableField(value = "customer_type")
    private Integer customerType;

    /**
     * 客户/企业名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    @TableField(value = "certificate_type")
    private Integer certificateType;

    /**
     * 证件编码（社会信用代码）
     */
    @TableField(value = "certificate_code")
    private String certificateCode;

    /**
     * 登记状态
     */
    @TableField(value = "check_in_state")
    private String checkInState;

    /**
     * 成立日期
     */
    @TableField(value = "establish_date")
    private LocalDate establishDate;

    /**
     * 注册资本
     */
    @TableField(value = "register_capital")
    private String registerCapital;

    /**
     * 实缴资本
     */
    @TableField(value = "paid_in_capital")
    private String paidInCapital;

    /**
     * 组织机构代码
     */
    @TableField(value = "organization_code")
    private String organizationCode;

    /**
     * 工商注册号
     */
    @TableField(value = "register_no")
    private String registerNo;

    /**
     * 纳税人识别号
     */
    @TableField(value = "taxpayer_no")
    private String taxpayerNo;

    /**
     * 企业类型
     */
    @TableField(value = "enterprise_type")
    private String enterpriseType;

    /**
     * 营业开始时间
     */
    @TableField(value = "open_start_time")
    private LocalDate openStartTime;

    /**
     * 营业结束时间
     */
    @TableField(value = "open_end_time")
    private LocalDate openEndTime;

    /**
     * 纳税人资质
     */
    @TableField(value = "taxpayer_qualification")
    private String taxpayerQualification;

    /**
     * 人员规模
     */
    @TableField(value = "staff_scale")
    private String staffScale;

    /**
     * 参保人数
     */
    @TableField(value = "insured_num")
    private String insuredNum;

    /**
     * 核准日期
     */
    @TableField(value = "approve_date")
    private LocalDate approveDate;

    /**
     * 省编码
     */
    @TableField(value = "province_code")
    private String provinceCode;

    /**
     * 省名称
     */
    @TableField(value = "province_name")
    private String provinceName;

    /**
     * 市编码
     */
    @TableField(value = "city_code")
    private String cityCode;

    /**
     * 市名称
     */
    @TableField(value = "city_name")
    private String cityName;

    /**
     * 区编码
     */
    @TableField(value = "district_code")
    private String districtCode;

    /**
     * 区名称
     */
    @TableField(value = "district_name")
    private String districtName;

    /**
     * 登记机关
     */
    @TableField(value = "registration_authority")
    private String registrationAuthority;

    /**
     * 进出口企业代码
     */
    @TableField(value = "import_export_enterprise_code")
    private String importExportEnterpriseCode;

    /**
     * 一级国标行业编码
     */
    @TableField(value = "first_industry_code")
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    @TableField(value = "first_industry_name")
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    @TableField(value = "second_industry_code")
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    @TableField(value = "second_industry_name")
    private String secondIndustryName;


    /**
     * 三级国标行业编码
     */
    @TableField(value = "third_industry_code")
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    @TableField(value = "third_industry_name")
    private String thirdIndustryName;


    /**
     * 四级国标行业编码
     */
    @TableField(value = "fourth_industry_code")
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    @TableField(value = "fourth_industry_name")
    private String fourthIndustryName;

    /**
     * 注册地址
     */
    @TableField(value = "register_address")
    private String registerAddress;

    /**
     * 经营范围
     */
    @TableField(value = "business_scope", exist = false)
    private String businessScope;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     */
    @TableField(value = "present_stage")
    private Integer presentStage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    @TableField(value = "create_way")
    private Integer createWay;

    /**
     * 客户来源
     */
    @TableField(value = "label_from")
    private String labelFrom;

    /**
     * 删除标记：1、删除，0、未删除
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建者凭证（废弃）
     */
    @TableField(value = "creator_key")
    private String creatorKey;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人凭证（废弃）
     */
    @TableField(value = "operator_key")
    private String operatorKey;

    /**
     * 更新人
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 无效标记：1、无效，0、有效
     */
    @TableField(value = "invalid_flag")
    private Integer invalidFlag;


    /**
     * 是否是ka用户
     */
    @TableField(value = "tag_ka")
    private Integer tagKa;

    /**
     * 是否是外贸用户
     */
    @TableField(value = "tag_waimao")
    private Integer tagWaimao;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_related")
    private Integer tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    @TableField(value = "tag_eco_to_menhu")
    private Integer tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    @TableField(value = "tag_eco_cust")
    private Integer tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    @TableField(value = "tag_menhu_digital")
    private Integer tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    @TableField(value = "tag_menhu_2023")
    private Integer tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     */
    @TableField(value = "tag_menhu_lowver")
    private Integer tagMenhuLowver;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_upgradeable_upgrade")
    private Integer tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_upgradeable")
    private Integer tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_renewable_renew")
    private Integer tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_renewable")
    private Integer tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    @TableField(value = "tag_cross_buy")
    private Integer tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    @TableField(value = "tag_pure_menhu")
    private Integer tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    @TableField(value = "tag_flag7")
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    @TableField(value = "tag_flag8")
    private String tagFlag8;

    /**
     * 科技型企业
     */
    @TableField(value = "tag_techcompany")
    private String tagTechcompany;

    /**
     * 门户新客户首次购买时间
     */
    @TableField(value = "tag_menhu_new_time")
    private Date tagMenhuNewTime;

    /**
     * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
     */
    @TableField(value = "tag_menhu_new_category")
    private String tagMenhuNewCategory;

    /**
     * 是否是报价客户 0:否 1:是
     */
    @TableField(value = "tag_quote_cust")
    private Integer tagQuoteCust;

    /**
     * 推荐客户的id
     */
    @TableField(value = "recommend_cust_id")
    private String recommendCustId;

    /**
     * 推荐客户创建时间
     */
    @TableField(value = "recommend_cust_create_time")
    private LocalDateTime recommendCustCreateTime;


    /**
     * 是否保有客户 0:否，1:是
     */
    @TableField(value = "tag_retain_cust")
    private Integer tagRetainCust;

    /**
     * 到期时间
     */
    @TableField(value = "tag_retain_time")
    private Date tagRetainTime;

    /**
     * 是否流失客户 0:否，1:是
     */
    @TableField(value = "tag_lost_cust")
    private Integer tagLostCust;

    /**
     * 流失时间
     */
    @TableField(value = "tag_lost_time")
    private Date tagLostTime;

    /**
     * 是否转介绍新客 0:否，1:是
     */
    @TableField(value = "tag_invited_new_cust")
    private Integer tagInvitedNewCust;

    /**
     * 是否合格新客 0:否，1:是
     */
    @TableField(value = "tag_qualified_new_cust")
    private Integer tagQualifiedNewCust;

    /**
     * 是否合格老客0:否，1:是
     */
    @TableField(value = "tag_qualified_old_cust")
    private Integer tagQualifiedOldCust;

    /**
     * 是否低价值客户 0:否 1:是
     */
    @TableField(value = "tag_low_value")
    private Integer tagLowValue;

    /**
     * 所属商务id
     */
    @TableField(value = "protect_saler_id")
    private String protectSalerId;

    /**
     * 部门id
     */
    @TableField(value = "protect_bussdept_id")
    private String protectBussdeptId;

    /**
     * 分公司ID
     */
    @TableField(value = "protect_subcompany_id")
    private String protectSubcompanyId;

    /**
     * 区域ID
     */
    @TableField(value = "protect_area_id")
    private String protectAreaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     */
    @TableField(value = "protect_status")
    private String protectStatus;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     */
    @TableField(value = "protect_cust_type")
    private String protectCustType;

    /**
     * 阶段性保护时间
     */
    @TableField(value = "protect_protect_time")
    private Date protectProtectTime;

    /**
     * 已保护天数(仅2023年以后数据)
     */
    @TableField(value = "cdp_protect_day")
    private Integer cdpProtectDay;

    /**
     * 本月打卡次数
     */
    @TableField(value = "cdp_current_month_clock_count")
    private Integer cdpCurrentMonthClockCount;

    /**
     * 累计打卡次数
     */
    @TableField(value = "cdp_clock_count")
    private Integer cdpClockCount;

    /**
     * 客户订单首次支付时间
     */
    @TableField(value = "cdp_first_payment_time")
    private Date cdpFirstPaymentTime;

    /**
     * 成为合格新客时间
     */
    @TableField(value = "tag_qualified_new_cust_time")
    private Date tagQualifiedNewCustTime;

    @TableField(value = "tag_flag12")
    private String tagFlag12;

	/**
	 * 	是否参加2025中国半导体产业与应用博览会（IC Expo） 0:否,1:是
	 */
	@TableField(value = "tag_china_icexpo_2025")
	private Integer tagChinaIcexpo2025;

	/**
	 * 是否参加2025CMEF第91届中国国际医疗器械博览会 0:否,1:是
	 */
	@TableField(value = "tag_china_cmef91_2025")
	private Integer tagChina91Cmef2025;

	/**
	 * 是否参加2025中国包装容器展 0:否,1:是
	 */
	@TableField(value = "tag_china_pack_containers_2025")
	private Integer tagChinaPackContainers2025;

	/**
	 * 是否参加2025全球领先国际塑料橡胶展 0:否,1:是
	 */
	@TableField(value = "tag_china_plastic_rubber_2025")
	private Integer tagChinaPlasticRubber2025;

	/**
	 * 是否参加2025慕尼黑上海电子展（electronica China） 0:否,1:是
	 */
	@TableField(value = "tag_china_electronica_2025")
	private Integer tagChinaElectronica2025;

	/**
	 * 第24届中国国际染料工业及有机颜料、纺织化学品展览会
	 */
	@TableField(value = "tag_china_dye_industry_2025")
	private Integer tagChinaDyeIndustry2025;

	/**
	 * 	是否参加2025第二十三届中国北京国际模型博览会 0:否,1:是
	 */
	@TableField(value = "tag_china_beijing_model23_2025")
	private Integer tagChinaBeijing23Model2025;

	/**
	 * 是否参加第26届中国环博会 IE expo China 2025 0:否,1:是
	 */
	@TableField(value = "tag_china_ie_expo26_2025")
	private Integer tagChina26IeExpo2025;

	/**
	 * 是否参加NEPCON China 2025亚洲电子生产设备暨微电子展工业展览会 0:否,1:是
	 */
	@TableField(value = "tag_china_nepcon_2025")
	private Integer tagChinaNepcon2025;

	/**
	 * 是否参加CDIIF 2025成都国际工业博览会 0:否,1:是
	 */
	@TableField(value = "tag_china_cdiif_2025")
	private Integer tagChinaCdiif2025;

	/**
	 * 是否参加2025第34届（北京）中国国际健康产业博览会 0:否,1:是
	 */
	@TableField(value = "tag_china_health34_2025")
	private Integer tagChina34Health2025;

	/**
	 * 是否参加Intertraffic China 2025第十八届国际交通工程、智能交通技术与设施展览会 0:否,1:是
	 */
	@TableField(value = "tag_china_intertraffic_2025")
	private Integer tagChinaIntertraffic2025;

	/**
	 * 是否参加2025第三十六届国际制冷、空调、供暖、通风及食品冷冻加工展览会 0:否,1:是
	 */
	@TableField(value = "tag_china_refrigeration_2025")
	private Integer tagChinaRefrigeration2025;

    /**
     * 是否是大客户（ka）新
     */
    @TableField(value = "tag_dakehu")
	private Integer tagDakehu;

    /**
     * 客户分层
     */
    @TableField(value = "customer_layer")
    private Integer customerLayer;

    /**
     * 客户等级
     */
    @TableField(value = "customer_grade")
    private Integer customerGrade;

    /**
     * CDP标签user_tag_sfvwcjkh20250530 未成交客户是否是vip判断依据
     */
    @TableField(value = "tag_weichengjiao_vip")
    private Integer tagWeichengjiaoVip;

    /**
     * 【销管口径】门户客户 0:否,1:是
     */
    @TableField(value = "tag_menhu")
    private Integer tagMenhu;
    /*
     * 法人
     */
    @TableField(value = "legal_person")
    private String legalPerson;

	/*
	 * 分发时间：指跨境ABM项目，leads分发给SDR的时间
	 */
	@TableField(value = "distribute_time")
	private Date distributeTime;

    /*
     * 保护释放时间
     */
    @TableField(value = "release_time")
    private Date releaseTime;

    /*
     * 线索产出时间
     */
    @TableField(value = "leads_create_time")
    private Date leadsCreateTime;

    /*
     * 来源编码
     */
    @TableField(value = "leads_source_code")
    private String leadsSourceCode;

    /*
     * 意愿编码
     */
    @TableField(value = "leads_intent_code")
    private String leadsIntentCode;

    /*
     * 事业部ID
     */
    @TableField(value = "protect_bu_id")
    private String protectBuId;

    /*
     * 保护结束时间
     */
    @TableField(value = "protect_protectend_time")
    private Date protectProtectendTime;

    /*
     * 最近拜访类型
     */
    @TableField(value = "last_visit_type")
    private Integer lastVisitType;

    /*
     * 最近拜访时间
     */
    @TableField(value = "last_follow_time")
    private Date lastFollowTime;

    /*
     * 最近上门时间
     */
    @TableField(value = "last_site_time")
    private Date lastSiteTime;

    /**
     * 绑定客户标识 0未绑定 1绑定
     */
    @TableField(value = "bind_flag")
    private Integer bindFlag;

    /**
     * 商机是否确认标记 0 正常（已经确认） 1 未确认 2 流转过一次-未确认
     */
    @TableField(value = "business_opportunity_confirmation_flag")
    private Integer businessOpportunityConfirmationFlag;

    /**
     * 分发渠道
     */
    @TableField(value = "distribute_channel")
    private Integer distributeChannel;

    /**
     * 推送销售审核状态
     */
    @TableField(value = "review_status")
    private Integer reviewStatus;

    /**
     * 跟进次数
     */
    @TableField(value = "follow_count")
    private Integer followCount;
    /**
     * 上门次数
     */
    @TableField(value = "site_count")
    private Integer siteCount;

    /**
     * SDR跟进次数
     */
    @TableField(value = "sdr_follow_count")
    private Integer sdrFollowCount;

    /**
     * SDR跟进时间
     */
    @TableField(value = "last_sdr_follow_time")
    private Date lastSdrFollowTime;

    /**
     * 回执终止时间
     */
    @TableField(value = "receipt_end_time")
    private Date receiptEndTime;

    /**
     * 回执状态：0：未回执 1： 已回执
     */
    @TableField(value = "receipt_flag")
    private Integer receiptFlag;

    /**
     * 需要回执的商务
     */
    @TableField(value = "receipt_saler_id")
    private String receiptSalerId;

    /**
     * 官网情况
     */
    @TableField(value = "official_website_flag")
    private Integer officialWebsiteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public Integer getReceiptFlag() {
        return receiptFlag;
    }

    public void setReceiptFlag(Integer receiptFlag) {
        this.receiptFlag = receiptFlag;
    }

    public String getReceiptSalerId() {
        return receiptSalerId;
    }

    public void setReceiptSalerId(String receiptSalerId) {
        this.receiptSalerId = receiptSalerId;
    }

    public Date getReceiptEndTime() {
        return receiptEndTime;
    }

    public void setReceiptEndTime(Date receiptEndTime) {
        this.receiptEndTime = receiptEndTime;
    }

    public String getTagFlag12() {
        return tagFlag12;
    }

    public void setTagFlag12(String tagFlag12) {
        this.tagFlag12 = tagFlag12;
    }

    /**
     * 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * 源数据ID
     */
    public String getSourceDataId() {
        return sourceDataId;
    }

    /**
     * 源数据ID
     */
    public void setSourceDataId(String sourceDataId) {
        this.sourceDataId = sourceDataId;
    }

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    public Integer getCustomerType() {
        return customerType;
    }

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    /**
     * 客户/企业名称
     */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * 客户/企业名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    public Integer getCertificateType() {
        return certificateType;
    }

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    /**
     * 证件编码（社会信用代码）
     */
    public String getCertificateCode() {
        return certificateCode;
    }

    /**
     * 证件编码（社会信用代码）
     */
    public void setCertificateCode(String certificateCode) {
        this.certificateCode = certificateCode;
    }

    /**
     * 登记状态
     */
    public String getCheckInState() {
        return checkInState;
    }

    /**
     * 登记状态
     */
    public void setCheckInState(String checkInState) {
        this.checkInState = checkInState;
    }

    /**
     * 成立日期
     */
    public LocalDate getEstablishDate() {
        return establishDate;
    }

    /**
     * 成立日期
     */
    public void setEstablishDate(LocalDate establishDate) {
        this.establishDate = establishDate;
    }

    /**
     * 注册资本
     */
    public String getRegisterCapital() {
        return registerCapital;
    }

    /**
     * 注册资本
     */
    public void setRegisterCapital(String registerCapital) {
        this.registerCapital = registerCapital;
    }

    /**
     * 实缴资本
     */
    public String getPaidInCapital() {
        return paidInCapital;
    }

    /**
     * 实缴资本
     */
    public void setPaidInCapital(String paidInCapital) {
        this.paidInCapital = paidInCapital;
    }

    /**
     * 组织机构代码
     */
    public String getOrganizationCode() {
        return organizationCode;
    }

    /**
     * 组织机构代码
     */
    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    /**
     * 工商注册号
     */
    public String getRegisterNo() {
        return registerNo;
    }

    /**
     * 工商注册号
     */
    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    /**
     * 纳税人识别号
     */
    public String getTaxpayerNo() {
        return taxpayerNo;
    }

    /**
     * 纳税人识别号
     */
    public void setTaxpayerNo(String taxpayerNo) {
        this.taxpayerNo = taxpayerNo;
    }

    /**
     * 企业类型
     */
    public String getEnterpriseType() {
        return enterpriseType;
    }

    /**
     * 企业类型
     */
    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    /**
     * 营业开始时间
     */
    public LocalDate getOpenStartTime() {
        return openStartTime;
    }

    /**
     * 营业开始时间
     */
    public void setOpenStartTime(LocalDate openStartTime) {
        this.openStartTime = openStartTime;
    }

    /**
     * 营业结束时间
     */
    public LocalDate getOpenEndTime() {
        return openEndTime;
    }

    /**
     * 营业结束时间
     */
    public void setOpenEndTime(LocalDate openEndTime) {
        this.openEndTime = openEndTime;
    }

    /**
     * 纳税人资质
     */
    public String getTaxpayerQualification() {
        return taxpayerQualification;
    }

    /**
     * 纳税人资质
     */
    public void setTaxpayerQualification(String taxpayerQualification) {
        this.taxpayerQualification = taxpayerQualification;
    }

    /**
     * 人员规模
     */
    public String getStaffScale() {
        return staffScale;
    }

    /**
     * 人员规模
     */
    public void setStaffScale(String staffScale) {
        this.staffScale = staffScale;
    }

    /**
     * 参保人数
     */
    public String getInsuredNum() {
        return insuredNum;
    }

    /**
     * 参保人数
     */
    public void setInsuredNum(String insuredNum) {
        this.insuredNum = insuredNum;
    }

    /**
     * 核准日期
     */
    public LocalDate getApproveDate() {
        return approveDate;
    }

    /**
     * 核准日期
     */
    public void setApproveDate(LocalDate approveDate) {
        this.approveDate = approveDate;
    }

    /**
     * 省编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 省编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * 省名称
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     * 省名称
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * 市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    /**
     * 市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 市名称
     */
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    /**
     * 区编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 区编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    /**
     * 区名称
     */
    public String getDistrictName() {
        return districtName;
    }

    /**
     * 区名称
     */
    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    /**
     * 登记机关
     */
    public String getRegistrationAuthority() {
        return registrationAuthority;
    }

    /**
     * 登记机关
     */
    public void setRegistrationAuthority(String registrationAuthority) {
        this.registrationAuthority = registrationAuthority;
    }

    /**
     * 进出口企业代码
     */
    public String getImportExportEnterpriseCode() {
        return importExportEnterpriseCode;
    }

    /**
     * 进出口企业代码
     */
    public void setImportExportEnterpriseCode(String importExportEnterpriseCode) {
        this.importExportEnterpriseCode = importExportEnterpriseCode;
    }

    /**
     * 一级国标行业编码
     */
    public String getFirstIndustryCode() {
        return firstIndustryCode;
    }

    /**
     * 一级国标行业编码
     */
    public void setFirstIndustryCode(String firstIndustryCode) {
        this.firstIndustryCode = firstIndustryCode;
    }

    /**
     * 一级国标行业名称
     */
    public String getFirstIndustryName() {
        return firstIndustryName;
    }

    /**
     * 一级国标行业名称
     */
    public void setFirstIndustryName(String firstIndustryName) {
        this.firstIndustryName = firstIndustryName;
    }

    /**
     * 二级国标行业编码
     */
    public String getSecondIndustryCode() {
        return secondIndustryCode;
    }

    /**
     * 二级国标行业编码
     */
    public void setSecondIndustryCode(String secondIndustryCode) {
        this.secondIndustryCode = secondIndustryCode;
    }

    /**
     * 二级国标行业名称
     */
    public String getSecondIndustryName() {
        return secondIndustryName;
    }

    /**
     * 二级国标行业名称
     */
    public void setSecondIndustryName(String secondIndustryName) {
        this.secondIndustryName = secondIndustryName;
    }

    /**
     * 注册地址
     */
    public String getRegisterAddress() {
        return registerAddress;
    }

    /**
     * 注册地址
     */
    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    /**
     * 经营范围
     */
    public String getBusinessScope() {
        return businessScope;
    }

    /**
     * 经营范围
     */
    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     */
    public Integer getPresentStage() {
        return presentStage;
    }

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     */
    public void setPresentStage(Integer presentStage) {
        this.presentStage = presentStage;
    }

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    public Integer getCreateWay() {
        return createWay;
    }

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    public void setCreateWay(Integer createWay) {
        this.createWay = createWay;
    }

    /**
     * 客户来源
     */
    public String getLabelFrom() {
        return labelFrom;
    }

    /**
     * 客户来源
     */
    public void setLabelFrom(String labelFrom) {
        this.labelFrom = labelFrom;
    }

    /**
     * 删除标记：1、删除，0、未删除
     */
    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    /**
     * 删除标记：1、删除，0、未删除
     */
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    /**
     * 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建者凭证（废弃）
     */
    public String getCreatorKey() {
        return creatorKey;
    }

    /**
     * 创建者凭证（废弃）
     */
    public void setCreatorKey(String creatorKey) {
        this.creatorKey = creatorKey;
    }

    /**
     * 创建者
     */
    public String getCreator() {
        return creator;
    }

    /**
     * 创建者
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 更新人凭证（废弃）
     */
    public String getOperatorKey() {
        return operatorKey;
    }

    /**
     * 更新人凭证（废弃）
     */
    public void setOperatorKey(String operatorKey) {
        this.operatorKey = operatorKey;
    }

    /**
     * 更新人
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 更新人
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 无效标记：1、无效，0、有效
     */
    public Integer getInvalidFlag() {
        return invalidFlag;
    }

    /**
     * 无效标记：1、无效，0、有效
     */
    public void setInvalidFlag(Integer invalidFlag) {
        this.invalidFlag = invalidFlag;
    }

    public Integer getTagKa() {
        return tagKa;
    }

    public void setTagKa(Integer tagKa) {
        this.tagKa = tagKa;
    }

    public Integer getTagWaimao() {
        return tagWaimao;
    }

    public void setTagWaimao(Integer tagWaimao) {
        this.tagWaimao = tagWaimao;
    }

    public Integer getTagMenhuRelated() {
        return tagMenhuRelated;
    }

    public void setTagMenhuRelated(Integer tagMenhuRelated) {
        this.tagMenhuRelated = tagMenhuRelated;
    }

    public Integer getTagEcoToMenhu() {
        return tagEcoToMenhu;
    }

    public void setTagEcoToMenhu(Integer tagEcoToMenhu) {
        this.tagEcoToMenhu = tagEcoToMenhu;
    }

    public Integer getTagEcoCust() {
        return tagEcoCust;
    }

    public void setTagEcoCust(Integer tagEcoCust) {
        this.tagEcoCust = tagEcoCust;
    }

    public Integer getTagMenhuDigital() {
        return tagMenhuDigital;
    }

    public void setTagMenhuDigital(Integer tagMenhuDigital) {
        this.tagMenhuDigital = tagMenhuDigital;
    }

    public Integer getTagMenhu2023() {
        return tagMenhu2023;
    }

    public void setTagMenhu2023(Integer tagMenhu2023) {
        this.tagMenhu2023 = tagMenhu2023;
    }

    public Integer getTagMenhuLowver() {
        return tagMenhuLowver;
    }

    public void setTagMenhuLowver(Integer tagMenhuLowver) {
        this.tagMenhuLowver = tagMenhuLowver;
    }

    public Integer getTagMenhuUpgradeableUpgrade() {
        return tagMenhuUpgradeableUpgrade;
    }

    public void setTagMenhuUpgradeableUpgrade(Integer tagMenhuUpgradeableUpgrade) {
        this.tagMenhuUpgradeableUpgrade = tagMenhuUpgradeableUpgrade;
    }

    public Integer getTagMenhuUpgradeable() {
        return tagMenhuUpgradeable;
    }

    public void setTagMenhuUpgradeable(Integer tagMenhuUpgradeable) {
        this.tagMenhuUpgradeable = tagMenhuUpgradeable;
    }

    public Integer getTagMenhuRenewableRenew() {
        return tagMenhuRenewableRenew;
    }

    public void setTagMenhuRenewableRenew(Integer tagMenhuRenewableRenew) {
        this.tagMenhuRenewableRenew = tagMenhuRenewableRenew;
    }

    public Integer getTagMenhuRenewable() {
        return tagMenhuRenewable;
    }

    public void setTagMenhuRenewable(Integer tagMenhuRenewable) {
        this.tagMenhuRenewable = tagMenhuRenewable;
    }

    public Integer getTagCrossBuy() {
        return tagCrossBuy;
    }

    public void setTagCrossBuy(Integer tagCrossBuy) {
        this.tagCrossBuy = tagCrossBuy;
    }

    public Integer getTagPureMenhu() {
        return tagPureMenhu;
    }

    public void setTagPureMenhu(Integer tagPureMenhu) {
        this.tagPureMenhu = tagPureMenhu;
    }

    public String getTagFlag7() {
        return tagFlag7;
    }

    public void setTagFlag7(String tagFlag7) {
        this.tagFlag7 = tagFlag7;
    }

    public String getTagFlag8() {
        return tagFlag8;
    }

    public void setTagFlag8(String tagFlag8) {
        this.tagFlag8 = tagFlag8;
    }

    public String getTagTechcompany() {
        return tagTechcompany;
    }

    public void setTagTechcompany(String tagTechcompany) {
        this.tagTechcompany = tagTechcompany;
    }

    public Date getTagMenhuNewTime() {
        return tagMenhuNewTime;
    }

    public void setTagMenhuNewTime(Date tagMenhuNewTime) {
        this.tagMenhuNewTime = tagMenhuNewTime;
    }

    public String getTagMenhuNewCategory() {
        return tagMenhuNewCategory;
    }

    public void setTagMenhuNewCategory(String tagMenhuNewCategory) {
        this.tagMenhuNewCategory = tagMenhuNewCategory;
    }

    public Integer getTagQuoteCust() {
        return tagQuoteCust;
    }

    public void setTagQuoteCust(Integer tagQuoteCust) {
        this.tagQuoteCust = tagQuoteCust;
    }

    public String getRecommendCustId() {
        return recommendCustId;
    }

    public void setRecommendCustId(String recommendCustId) {
        this.recommendCustId = recommendCustId;
    }

    public Integer getTagRetainCust() {
        return tagRetainCust;
    }

    public void setTagRetainCust(Integer tagRetainCust) {
        this.tagRetainCust = tagRetainCust;
    }

    public Date getTagRetainTime() {
        return tagRetainTime;
    }

    public void setTagRetainTime(Date tagRetainTime) {
        this.tagRetainTime = tagRetainTime;
    }

    public Integer getTagLostCust() {
        return tagLostCust;
    }

    public void setTagLostCust(Integer tagLostCust) {
        this.tagLostCust = tagLostCust;
    }

    public Date getTagLostTime() {
        return tagLostTime;
    }

    public void setTagLostTime(Date tagLostTime) {
        this.tagLostTime = tagLostTime;
    }

    public Integer getTagInvitedNewCust() {
        return tagInvitedNewCust;
    }

    public void setTagInvitedNewCust(Integer tagInvitedNewCust) {
        this.tagInvitedNewCust = tagInvitedNewCust;
    }

    public Integer getTagQualifiedNewCust() {
        return tagQualifiedNewCust;
    }

    public void setTagQualifiedNewCust(Integer tagQualifiedNewCust) {
        this.tagQualifiedNewCust = tagQualifiedNewCust;
    }

    public Integer getTagQualifiedOldCust() {
        return tagQualifiedOldCust;
    }

    public void setTagQualifiedOldCust(Integer tagQualifiedOldCust) {
        this.tagQualifiedOldCust = tagQualifiedOldCust;
    }

    public Integer getTagLowValue() {
        return tagLowValue;
    }

    public void setTagLowValue(Integer tagLowValue) {
        this.tagLowValue = tagLowValue;
    }

    public LocalDateTime getRecommendCustCreateTime() {
        return recommendCustCreateTime;
    }

    public void setRecommendCustCreateTime(LocalDateTime recommendCustCreateTime) {
        this.recommendCustCreateTime = recommendCustCreateTime;
    }

    public String getProtectSalerId() {
        return protectSalerId;
    }

    public void setProtectSalerId(String protectSalerId) {
        this.protectSalerId = protectSalerId;
    }

    public String getProtectBussdeptId() {
        return protectBussdeptId;
    }

    public void setProtectBussdeptId(String protectBussdeptId) {
        this.protectBussdeptId = protectBussdeptId;
    }

    public String getProtectSubcompanyId() {
        return protectSubcompanyId;
    }

    public void setProtectSubcompanyId(String protectSubcompanyId) {
        this.protectSubcompanyId = protectSubcompanyId;
    }

    public String getProtectAreaId() {
        return protectAreaId;
    }

    public void setProtectAreaId(String protectAreaId) {
        this.protectAreaId = protectAreaId;
    }

    public String getProtectStatus() {
        return protectStatus;
    }

    public void setProtectStatus(String protectStatus) {
        this.protectStatus = protectStatus;
    }

    public String getProtectCustType() {
        return protectCustType;
    }

    public void setProtectCustType(String protectCustType) {
        this.protectCustType = protectCustType;
    }

    public Date getProtectProtectTime() {
        return protectProtectTime;
    }

    public void setProtectProtectTime(Date protectProtectTime) {
        this.protectProtectTime = protectProtectTime;
    }

    public Integer getCdpProtectDay() {
        return cdpProtectDay;
    }

    public void setCdpProtectDay(Integer cdpProtectDay) {
        this.cdpProtectDay = cdpProtectDay;
    }

    public Integer getCdpCurrentMonthClockCount() {
        return cdpCurrentMonthClockCount;
    }

    public void setCdpCurrentMonthClockCount(Integer cdpCurrentMonthClockCount) {
        this.cdpCurrentMonthClockCount = cdpCurrentMonthClockCount;
    }

    public Integer getCdpClockCount() {
        return cdpClockCount;
    }

    public void setCdpClockCount(Integer cdpClockCount) {
        this.cdpClockCount = cdpClockCount;
    }

    public Date getCdpFirstPaymentTime() {
        return cdpFirstPaymentTime;
    }

    public void setCdpFirstPaymentTime(Date cdpFirstPaymentTime) {
        this.cdpFirstPaymentTime = cdpFirstPaymentTime;
    }

    public Date getTagQualifiedNewCustTime() {
        return tagQualifiedNewCustTime;
    }

    public void setTagQualifiedNewCustTime(Date tagQualifiedNewCustTime) {
        this.tagQualifiedNewCustTime = tagQualifiedNewCustTime;
    }

	public Integer getTagChinaIcexpo2025() {
		return tagChinaIcexpo2025;
	}

	public void setTagChinaIcexpo2025(Integer tagChinaIcexpo2025) {
		this.tagChinaIcexpo2025 = tagChinaIcexpo2025;
	}

	public Integer getTagChina91Cmef2025() {
		return tagChina91Cmef2025;
	}

	public void setTagChina91Cmef2025(Integer tagChina91Cmef2025) {
		this.tagChina91Cmef2025 = tagChina91Cmef2025;
	}

	public Integer getTagChinaPackContainers2025() {
		return tagChinaPackContainers2025;
	}

	public void setTagChinaPackContainers2025(Integer tagChinaPackContainers2025) {
		this.tagChinaPackContainers2025 = tagChinaPackContainers2025;
	}

	public Integer getTagChinaPlasticRubber2025() {
		return tagChinaPlasticRubber2025;
	}

	public void setTagChinaPlasticRubber2025(Integer tagChinaPlasticRubber2025) {
		this.tagChinaPlasticRubber2025 = tagChinaPlasticRubber2025;
	}

	public Integer getTagChinaElectronica2025() {
		return tagChinaElectronica2025;
	}

	public void setTagChinaElectronica2025(Integer tagChinaElectronica2025) {
		this.tagChinaElectronica2025 = tagChinaElectronica2025;
	}

	public Integer getTagChinaDyeIndustry2025() {
		return tagChinaDyeIndustry2025;
	}

	public void setTagChinaDyeIndustry2025(Integer tagChinaDyeIndustry2025) {
		this.tagChinaDyeIndustry2025 = tagChinaDyeIndustry2025;
	}

	public Integer getTagChinaBeijing23Model2025() {
		return tagChinaBeijing23Model2025;
	}

	public void setTagChinaBeijing23Model2025(Integer tagChinaBeijing23Model2025) {
		this.tagChinaBeijing23Model2025 = tagChinaBeijing23Model2025;
	}

	public Integer getTagChina26IeExpo2025() {
		return tagChina26IeExpo2025;
	}

	public void setTagChina26IeExpo2025(Integer tagChina26IeExpo2025) {
		this.tagChina26IeExpo2025 = tagChina26IeExpo2025;
	}

	public Integer getTagChinaNepcon2025() {
		return tagChinaNepcon2025;
	}

	public void setTagChinaNepcon2025(Integer tagChinaNepcon2025) {
		this.tagChinaNepcon2025 = tagChinaNepcon2025;
	}

	public Integer getTagChinaCdiif2025() {
		return tagChinaCdiif2025;
	}

	public void setTagChinaCdiif2025(Integer tagChinaCdiif2025) {
		this.tagChinaCdiif2025 = tagChinaCdiif2025;
	}

	public Integer getTagChina34Health2025() {
		return tagChina34Health2025;
	}

	public void setTagChina34Health2025(Integer tagChina34Health2025) {
		this.tagChina34Health2025 = tagChina34Health2025;
	}

	public Integer getTagChinaIntertraffic2025() {
		return tagChinaIntertraffic2025;
	}

	public void setTagChinaIntertraffic2025(Integer tagChinaIntertraffic2025) {
		this.tagChinaIntertraffic2025 = tagChinaIntertraffic2025;
	}

	public Integer getTagChinaRefrigeration2025() {
		return tagChinaRefrigeration2025;
	}

	public void setTagChinaRefrigeration2025(Integer tagChinaRefrigeration2025) {
		this.tagChinaRefrigeration2025 = tagChinaRefrigeration2025;
	}

    public Integer getCustomerLayer() {
        return customerLayer;
    }

    public void setCustomerLayer(Integer customerLayer) {
        this.customerLayer = customerLayer;
    }

    public Integer getCustomerGrade() {
        return customerGrade;
    }

    public void setCustomerGrade(Integer customerGrade) {
        this.customerGrade = customerGrade;
    }

    public Integer getTagDakehu() {
        return tagDakehu;
    }

    public void setTagDakehu(Integer tagDakehu) {
        this.tagDakehu = tagDakehu;
    }

    public Integer getTagWeichengjiaoVip() {
        return tagWeichengjiaoVip;
    }

    public void setTagWeichengjiaoVip(Integer tagWeichengjiaoVip) {
        this.tagWeichengjiaoVip = tagWeichengjiaoVip;
    }

    public Integer getTagMenhu() {
        return tagMenhu;
    }

    public void setTagMenhu(Integer tagMenhu) {
        this.tagMenhu = tagMenhu;
    }

    public String getThirdIndustryCode() {
        return thirdIndustryCode;
    }

    public void setThirdIndustryCode(String thirdIndustryCode) {
        this.thirdIndustryCode = thirdIndustryCode;
    }

    public String getThirdIndustryName() {
        return thirdIndustryName;
    }

    public void setThirdIndustryName(String thirdIndustryName) {
        this.thirdIndustryName = thirdIndustryName;
    }

    public String getFourthIndustryCode() {
        return fourthIndustryCode;
    }

    public void setFourthIndustryCode(String fourthIndustryCode) {
        this.fourthIndustryCode = fourthIndustryCode;
    }

    public String getFourthIndustryName() {
        return fourthIndustryName;
    }

    public void setFourthIndustryName(String fourthIndustryName) {
        this.fourthIndustryName = fourthIndustryName;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public Date getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(Date distributeTime) {
        this.distributeTime = distributeTime;
    }

    public Date getLeadsCreateTime() {
        return leadsCreateTime;
    }

    public void setLeadsCreateTime(Date leadsCreateTime) {
        this.leadsCreateTime = leadsCreateTime;
    }

    public String getLeadsSourceCode() {
        return leadsSourceCode;
    }

    public void setLeadsSourceCode(String leadsSourceCode) {
        this.leadsSourceCode = leadsSourceCode;
    }

    public String getLeadsIntentCode() {
        return leadsIntentCode;
    }

    public void setLeadsIntentCode(String leadsIntentCode) {
        this.leadsIntentCode = leadsIntentCode;
    }

    public String getProtectBuId() {
        return protectBuId;
    }

    public void setProtectBuId(String protectBuId) {
        this.protectBuId = protectBuId;
    }

    public Date getProtectProtectendTime() {
        return protectProtectendTime;
    }

    public void setProtectProtectendTime(Date protectProtectendTime) {
        this.protectProtectendTime = protectProtectendTime;
    }

    public Integer getLastVisitType() {
        return lastVisitType;
    }

    public void setLastVisitType(Integer lastVisitType) {
        this.lastVisitType = lastVisitType;
    }

    public Date getLastFollowTime() {
        return lastFollowTime;
    }

    public void setLastFollowTime(Date lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
    }

    public Date getLastSiteTime() {
        return lastSiteTime;
    }

    public void setLastSiteTime(Date lastSiteTime) {
        this.lastSiteTime = lastSiteTime;
    }

    public Integer getBindFlag() {
        return bindFlag;
    }

    public void setBindFlag(Integer bindFlag) {
        this.bindFlag = bindFlag;
    }

    public Integer getBusinessOpportunityConfirmationFlag() {
        return businessOpportunityConfirmationFlag;
    }

    public void setBusinessOpportunityConfirmationFlag(Integer businessOpportunityConfirmationFlag) {
        this.businessOpportunityConfirmationFlag = businessOpportunityConfirmationFlag;
    }

    public Integer getDistributeChannel() {
        return distributeChannel;
    }

    public void setDistributeChannel(Integer distributeChannel) {
        this.distributeChannel = distributeChannel;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public Integer getFollowCount() {
        return followCount;
    }

    public void setFollowCount(Integer followCount) {
        this.followCount = followCount;
    }

    public Integer getSiteCount() {
        return siteCount;
    }

    public void setSiteCount(Integer siteCount) {
        this.siteCount = siteCount;
    }

    public Integer getSdrFollowCount() {
        return sdrFollowCount;
    }

    public void setSdrFollowCount(Integer sdrFollowCount) {
        this.sdrFollowCount = sdrFollowCount;
    }

    public Date getLastSdrFollowTime() {
        return lastSdrFollowTime;
    }

    public void setLastSdrFollowTime(Date lastSdrFollowTime) {
        this.lastSdrFollowTime = lastSdrFollowTime;
    }

    public Integer getOfficialWebsiteFlag() {
        return officialWebsiteFlag;
    }

    public void setOfficialWebsiteFlag(Integer officialWebsiteFlag) {
        this.officialWebsiteFlag = officialWebsiteFlag;
    }
}