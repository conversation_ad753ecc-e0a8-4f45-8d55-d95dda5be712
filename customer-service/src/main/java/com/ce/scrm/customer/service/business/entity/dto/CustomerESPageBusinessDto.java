package com.ce.scrm.customer.service.business.entity.dto;

import com.ce.scrm.customer.service.annotation.*;
import lombok.Data;

/**
 * @project scrm-customer
 * <AUTHOR>
 * @date 2025/7/14 10:43:56
 * @version 1.0
 * 查询客户ES的条件
 * 由于采用统一的ES查询方式，查询的字段都采用String，尽量不采用Integer等数据类型,Date等时间类型!!!
 */
@Data
public class CustomerESPageBusinessDto {

    /**
     * 查询成立结束日期
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd")
    private String establishDate;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 搜客宝公司唯一ID
     */
    private String pid;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    @EsNumeric
    private String customerType;

    /**
     * 客户/企业名称
     */
    @EsMatch
    private String customerName;

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    @EsNumeric
    private String certificateType;

    /**
     * 证件编码
     */
    private String certificateCode;

    /**
     * 登记状态
     */
    private String checkInState;

    /**
     * 注册资本
     */
    private String registerCapital;

    /**
     * 工商注册号
     */
    private String registerNo;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 营业开始时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd")
    private String openStartTime;

    /**
     * 营业结束时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd")
    private String openEndTime;

    /**
     * 核准日期
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd")
    private String approveDate;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 登记机关
     */
    private String registrationOrg;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;


    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;


    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册地址
     */
    @EsMatch
    private String registerAddress;

    /**
     * 经营范围
     */
    @EsMatch
    private String businessScope;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     * 需要db-es转换
     */
    @EsNumeric
    private String stage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    @EsNumeric
    private String createWay;

    /**
     * 客户来源
     */
    private String labelFrom;

    /**
     * 删除标记：1、删除，0、未删除
     */
    @EsNumeric
    private String deleteFlag;

    /**
     * 创建时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String createTime;

    /**
     * 创建者凭证（废弃）
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String updateTime;

    /**
     * 更新人凭证（废弃）
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 无效标记：1、无效，0、有效
     */
    @EsNumeric
    private String invalidFlag;

    /**
     * 是否是外贸用户
     */
    @EsNumeric
    private String tagWaimao;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    @EsNumeric
    private String tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    @EsNumeric
    private String tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    @EsNumeric
    private String tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    @EsNumeric
    private String tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    @EsNumeric
    private String tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     * 需要db-es转换
     */
    @EsNumeric
    private String tagMenhuLower;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    @EsNumeric
    private String tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    @EsNumeric
    private String tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    @EsNumeric
    private String tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    @EsNumeric
    private String tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    @EsNumeric
    private String tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    @EsNumeric
    private String tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    private String tagFlag8;

    /**
     * 科技型企业
     */
    private String tagTechcompany;

    /**
     * 门户新客户首次购买时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String tagMenhuNewTime;

    /**
     * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
     */
    private String tagMenhuNewCategory;

    /**
     * 是否是报价客户 0:否 1:是
     */
    @EsNumeric
    private String tagQuoteCust;

    /**
     * 推荐客户的id
     */
    private String recommendCustId;

    /**
     * 推荐客户创建时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String recommendCustCreateTime;

    /**
     * 是否保有客户 0:否，1:是
     */
    @EsNumeric
    private String tagRetainCust;

    /**
     * 到期时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String tagRetainTime;

    /**
     * 是否流失客户 0:否，1:是
     */
    @EsNumeric
    private String tagLostCust;

    /**
     * 流失时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String tagLostTime;

    /**
     * 是否转介绍新客 0:否，1:是
     */
    @EsNumeric
    private String tagInvitedNewCust;

    /**
     * 是否合格新客 0:否，1:是
     */
    @EsNumeric
    private String tagQualifiedNewCust;

    /**
     * 是否合格老客0:否，1:是
     */
    @EsNumeric
    private String tagQualifiedOldCust;

    /**
     * 是否低价值客户 0:否 1:是
     */
    @EsNumeric
    private String tagLowValue;

    /**
     * 所属商务id
     * 需要db-es转换
     */
    private String salerId;

    /**
     * 部门id
     * 需要db-es转换
     */
    private String deptId;

    /**
     * 分公司ID
     * 需要db-es转换
     */
    private String subId;

    /**
     * 区域ID
     * 需要db-es转换
     */
    private String areaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     * 需要db-es转换
     */
    private String status;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     * 需要db-es转换
     */
    private String tradeProductType;

    /**
     * 阶段性保护时间
     * 需要db-es转换
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String protectTime;

    /**
     * 已保护天数(仅2023年以后数据)
     */
    @EsNumeric
    private String protectDay;

    /**
     * 本月打卡次数
     * 需要db-es转换
     */
    @EsNumeric
    private String clockCountMonth;

    /**
     * 累计打卡次数
     * 需要db-es转换
     */
    @EsNumeric
    private String clockCount;

    /**
     * 客户订单首次支付时间
     * 需要db-es转换
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String firstPaymentTime;

    /**
     * 成为合格新客时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String tagQualifiedNewCustTime;

    private String tagFlag12;

    /**
     * 是否是大客户（ka）新
     */
    @EsNumeric
    private String tagDakehu;

    /**
     * 客户分层
     */
    @EsNumeric
    private String customerLayer;

    /**
     * 客户等级
     */
    @EsNumeric
    private String customerGrade;

    /**
     * CDP标签user_tag_sfvwcjkh20250530 未成交客户是否是vip判断依据
     */
    @EsNumeric
    private String tagWeichengjiaoVip;

    /**
     * 法人
     */
    private String legalPerson;

    /*
     * 分发时间：指跨境ABM项目，leads分发给SDR的时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String distributeTime;

    /*
     * 保护释放时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String releaseTime;

    /*
     * 线索产出时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String leadsCreateTime;

    /*
     * 来源编码
     */
    private String leadsSourceCode;

    /*
     * 意愿编码
     */
    private String leadsIntentCode;

    /*
     * 事业部ID
     */
    private String buId;

    /*
     * 保护结束时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String protectEndTime;

    /*
     * 最近拜访类型
     */
    @EsNumeric
    private String lastVisitType;

    /*
     * 最近拜访时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String lastFollowTime;

    /*
     * 最近上门时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String lastSiteTime;

    /**
     * 分发渠道
     */
    private String distributeChannel;

    /**
     * 推送销售审核状态
     */
    private String reviewStatus;

    /**
     * 跟进次数
     */
    @EsNumeric
    private String followCount;
    /**
     * 上门次数
     */
    @EsNumeric
    private String siteCount;

    /*
     * SDR跟进时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String lastSdrFollowTime;

    /**
     * SDR跟进次数
     */
    @EsNumeric
    private String sdrFollowCount;

    /**
     * 回执终止时间
     */
    @EsRange
    @EsDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    private String receiptEndTime;

    /**
     * 标签得分
     */
    private String tagScore;

    /**
     * 客户标签列表
     */
    private String tagList;
    /**
     * 页号
     */
    @EsIgnore
    private Integer pageNum = 1;

    /**
     * 页码
     */
    @EsIgnore
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @EsIgnore
    private String orderBy;

    /**
     * 是否正序
     */
    @EsIgnore
    private boolean ascFlag;

}