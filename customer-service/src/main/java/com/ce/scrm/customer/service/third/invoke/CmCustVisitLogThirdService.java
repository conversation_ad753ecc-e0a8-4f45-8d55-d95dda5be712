package com.ce.scrm.customer.service.third.invoke;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import com.ce.scrm.customer.service.third.entity.view.CmCustVisitLogThirdView;
import com.ce.scrm.extend.dubbo.api.CmCustVisitLogDubboService;
import com.ce.scrm.extend.dubbo.entity.request.CmCustVisitLogReq;
import com.ce.scrm.extend.dubbo.entity.response.CmCustVisitLogRes;
import com.ce.scrm.extend.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 跟进记录
 * <AUTHOR>
 * @date 2024/1/15 11:16
 * @version 1.0.0
 */
@Slf4j
@Service
public class CmCustVisitLogThirdService {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", timeout = 10000, check = false)
    private CmCustVisitLogDubboService cmCustVisitLogDubboService;

    public DubboPageInfo<CmCustVisitLogRes> getLastOnePageByCondition(String customerId,String recordWay) {
        log.info("getLastOnePageByCondition,customerId={},recordWay={}",customerId,recordWay);
        if (StringUtils.isBlank(customerId)) {
            return null;
        }

        CmCustVisitLogReq cmCustVisitLogReq = new CmCustVisitLogReq();
        cmCustVisitLogReq.setCustId(customerId);
        cmCustVisitLogReq.setRecordWay(recordWay);
        DubboPageInfo<CmCustVisitLogRes> dubboResult = cmCustVisitLogDubboService.getLastOnePageByCondition(cmCustVisitLogReq);
        if (dubboResult == null) {
            return DubboPageInfo.empty(1, 1);
        }
        return dubboResult;

    }

    public Integer getCountByCustomerAndRecordWay(String customerId,String recordWay) {
        log.info("getProtectByCustomerId,customerId={},recordWay={}",customerId,recordWay);
        if (StringUtils.isBlank(customerId)) {
            return null;
        }

        CmCustVisitLogReq cmCustVisitLogReq = new CmCustVisitLogReq();
        cmCustVisitLogReq.setCustId(customerId);
        cmCustVisitLogReq.setRecordWay(recordWay);
        DubboResult<Integer> dubboResult = cmCustVisitLogDubboService.getCountByCondition(cmCustVisitLogReq);
        if (!dubboResult.checkSuccess()) {
            log.error("getProtectByCustomerId,customerId={},recordWay={}",customerId,recordWay);
            return null;
        }

        return dubboResult.getData();

    }

}