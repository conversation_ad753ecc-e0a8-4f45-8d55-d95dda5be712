package com.ce.scrm.customer.service.third.invoke;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dubbo.api.CustomerProtectDubbo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import com.ce.scrm.customer.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.extend.dubbo.api.IBigDataDubbo;
import com.ce.scrm.extend.dubbo.entity.view.BigDataCompanyDetailView;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 保护跟进
 * <AUTHOR>
 * @date 2024/1/15 11:16
 * @version 1.0.0
 */
@Slf4j
@Service
public class ProtectThirdService {

    @DubboReference(group = "scrm-center-api", version = "1.0.0", timeout = 10000, check = false)
    private CustomerProtectDubbo customerProtectDubbo;

    public CustomerProtectDubboVew getProtectByCustomerId(String customerId) {
        log.info("调用获取保护信息数据dubbo，入参为:{}", customerId);
        if (StringUtils.isBlank(customerId)) {
            return null;
        }

        DubboResult<CustomerProtectDubboVew> customerProtectDubboVewDubboResult = customerProtectDubbo.selectCustomerById(customerId);
        if (!customerProtectDubboVewDubboResult.checkSuccess()) {
            log.error("调用获取保护信息数据dubbo失败，入参为:{}", customerId);
            return null;
        }

        return customerProtectDubboVewDubboResult.getData();

    }

}