package com.ce.scrm.customer.service.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.customer.service.annotation.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project scrm-customer
 * @ClassName CustomerESUtil
 * @date 2025/8/7 08:57:59
 * @description 客户ElasticSearch通用查询工具类
 * IN / NOT IN："1,2,3"，"!1,2,3"
 * OR 分组（跨字段）："|group:1,2"
 * Exists/Not Exists: 字段值为："@"或者 "!@"
 * RANGE："2024-01-01,2024-12-31"，支持 LocalDate / LocalDateTime，@RangeFormat 可自定义格式
 * 新的RANGE方式:
 * range查询（字符串方式）前缀 r: >100 / >=100 / <100 / <=100 100..200 （闭区间）  100..<200 （左闭右开）
 * 示例： r:>100,r:100..200,r:100..<200等
 * 多字段排序：orderBy="field1:asc,field2:desc"，ascFlag 控制默认排序方向
 */
@Slf4j
public class CustomerESUtil {

    private static class ParsedValue {
        boolean isNot;
        boolean isOr;
        boolean isMatch;
        boolean isExists;
        boolean isRange;
        Object rangeFrom;
        Object rangeTo;
        boolean includeFrom = true;
        boolean includeTo = true;
        String orGroupId;
        List<Object> values;
    }
    /**
     * <AUTHOR>
     * @date 2025/7/14 18:46:42
     * @return org.elasticsearch.index.query.BoolQueryBuilder
     * @desc 根据字段类型,字段值构建通用查询条件
     * 新的查询方式尽量用前缀的方式，兼容注解的方式
     */
    public static BoolQueryBuilder buildBoolQueryBuilder(Object dto) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        Map<String, RangeQueryBuilder> rangeMap = new HashMap<>();
        Map<String, BoolQueryBuilder> orGroups = new HashMap<>();
        Field[] fields = dto.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(EsIgnore.class)) {
                continue; // 忽略字段
            }

            field.setAccessible(true);
            try {
                Object value = field.get(dto);
                if (isEffectivelyEmpty(value)) continue;

                String fieldName = field.getName();
                String esFieldName = fieldName;
                // 读取字段名映射
                if (field.isAnnotationPresent(EsField.class)) {
                    esFieldName = field.getAnnotation(EsField.class).name();
                }

                boolean useMatch = field.isAnnotationPresent(EsMatch.class);
                boolean forceNumeric = field.isAnnotationPresent(EsNumeric.class);
                Class<?> numericType = forceNumeric ? field.getAnnotation(EsNumeric.class).type() : null;
                EsRange esRange = field.getAnnotation(EsRange.class);
                EsDateFormat dateFormat = field.getAnnotation(EsDateFormat.class);
                boolean isDate = dateFormat != null;
                Class<?> fieldType = field.getType();

                //tagList是特殊的嵌入类型的字段,保存tag,所以特殊处理
                if ("tagList".equalsIgnoreCase(fieldName)) {
                    List<String> tagList = Arrays.stream(value.toString().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .collect(Collectors.toList());
                    QueryBuilder nestedQuery = QueryBuilders.nestedQuery(
                            "tagList",
                            QueryBuilders.termsQuery("tagList.tagCode", tagList),
                            ScoreMode.None);

                    boolQuery.filter(nestedQuery);
                    continue;
                }
                ParsedValue parsed = parseValue(value);

                if (parsed.isExists) {
                    QueryBuilder existsQuery = QueryBuilders.existsQuery(esFieldName);
                    if (parsed.isNot) {
                        boolQuery.mustNot(existsQuery);
                    } else {
                        boolQuery.filter(existsQuery);
                    }
                    continue;
                }
                QueryBuilder singleQuery = buildQuery(esFieldName, parsed, field, value, rangeMap);

                if (singleQuery == null) {
                    continue;
                }
                EsOrGroup orGroup = field.getAnnotation(EsOrGroup.class);
                // OR 分组
                if (parsed.isOr) {
                    String groupId = parsed.orGroupId != null ? parsed.orGroupId : "_default";
                    BoolQueryBuilder groupQuery = orGroups.computeIfAbsent(groupId, k -> QueryBuilders.boolQuery());
                    groupQuery.should(singleQuery);
                    continue;
                }
                //兼容注解方式
                else if (orGroup != null) {
                    String groupName = orGroup.value();
                    BoolQueryBuilder groupQuery = orGroups.computeIfAbsent(groupName, k -> QueryBuilders.boolQuery());
                    groupQuery.should(singleQuery);
                    continue;
                }

                // 普通 AND
                if (parsed.isNot) {
                    boolQuery.mustNot(singleQuery);
                } else {
                    if (parsed.isMatch) {
                        boolQuery.must(singleQuery);
                    } else{
                        boolQuery.filter(singleQuery);
                    }
                }

            } catch (IllegalAccessException e) {
                log.error("buildBoolQueryBuilder失败,param={}", JSONObject.toJSONString(dto));
            }
        }
        // 加入 range 查询
        for (RangeQueryBuilder rangeQuery : rangeMap.values()) {
            boolQuery.filter(rangeQuery);
        }
        // 把所有 OR 组拼进主 boolQuery
        for (BoolQueryBuilder groupQuery : orGroups.values()) {
            groupQuery.minimumShouldMatch(1);
            boolQuery.filter(groupQuery);
        }
        return boolQuery;
    }

    /**
     * <AUTHOR>
     * @date 2025/7/23 09:30:27
     * @return void
     * @desc 设置排序条件,field1:asc,field2:desc,field3:field4,字段没有指定排序方式的话，用全局的排序方式
     */
    public static void setSort(Object dto, SearchSourceBuilder sourceBuilder) {
        try {
            Field orderByField = dto.getClass().getDeclaredField("orderBy");
            Field ascFlagField = dto.getClass().getDeclaredField("ascFlag");

            orderByField.setAccessible(true);
            ascFlagField.setAccessible(true);

            String orderBy = (String) orderByField.get(dto);
            //基础类型 boolean 没有 null 值，但默认是 false, 即默认是降序
            boolean ascFlag = ascFlagField.getBoolean(dto);

            if (orderBy == null || orderBy.trim().isEmpty()) {
                return; // 没有排序字段，直接跳过
            }

            String[] fields = orderBy.split(",");
            for (String fieldSpec : fields) {
                fieldSpec = fieldSpec.trim();
                if (fieldSpec.isEmpty()) continue;

                String fieldName = fieldSpec;
                SortOrder sortOrder = ascFlag ? SortOrder.ASC : SortOrder.DESC;

                // 支持 "field:asc" 这种写法
                if (fieldSpec.contains(":")) {
                    String[] parts = fieldSpec.split(":");
                    fieldName = parts[0].trim();
                    if (parts.length > 1) {
                        String dir = parts[1].trim().toLowerCase();
                        if ("asc".equals(dir)) {
                            sortOrder = SortOrder.ASC;
                        } else if ("desc".equals(dir)) {
                            sortOrder = SortOrder.DESC;
                        }
                    }
                }
                if (!fieldName.isEmpty()) {
                    sourceBuilder.sort(fieldName, sortOrder);
                }
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("setSourceBuilder失败,param={}", JSONObject.toJSONString(dto));
        }
    }

    /**
     * <AUTHOR>
     * @date 2025/7/23 09:29:47
     * @return boolean
     * @desc 判断值是否为 null、空字符串、空集合、空数组
     */
    private static boolean isEffectivelyEmpty(Object value) {
        if (value == null) return true;
        if (value instanceof String && ((String) value).trim().isEmpty()) return true;
        if (value instanceof Collection && ((Collection<?>) value).isEmpty()) return true;
        if (value.getClass().isArray() && Array.getLength(value) == 0) return true;
        return false;
    }

    /**
     * <AUTHOR>
     * @date 2025/7/23 09:29:14
     * @return java.util.Date
     * @desc LocalDateTime转换为Date类型
     */
    private static Date safeConvertLocalDateTimeToDate(LocalDateTime localDateTime) {
        return localDateTime != null
                ? Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant())
                : null;
    }

    /** 数组/集合统一转 List */
    private static List<Object> toList(Object value) {
        if (value instanceof Collection) {
            return ((Collection<?>) value).stream().filter(Objects::nonNull).collect(Collectors.toList());
        } else if (value.getClass().isArray()) {
            return Arrays.stream((Object[]) value).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /** 数组/集合 → 指定数值类型 */
    private static List<Object> convertListToNumeric(List<Object> list, Class<?> type) {
        return list.stream().map(val -> convert(val, type)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /** 单值转换 */
    private static Object convert(Object val, Class<?> type) {
        if (!(val instanceof String)) return val;
        try {
            String str = ((String) val).trim();
            if (type == Long.class) return Long.parseLong(str);
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private static Object convertRangeValue(String val, boolean isDate, boolean autoCompleteTime, boolean isStart) {
        if (isDate) {
            // 日期型：自动补时分秒
            if (val.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                if (autoCompleteTime) {
                    return isStart ? val + "T00:00:00" : val + "T23:59:59";
                }
            }
            //匹配 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
            else if (val.matches("^(\\d{4}-\\d{2}-\\d{2})\\s+(\\d{2}:\\d{2}:\\d{2})$")) {
                return val.replaceFirst("^(\\d{4}-\\d{2}-\\d{2})\\s+(\\d{2}:\\d{2}:\\d{2})$", "$1T$2");
            }
            return val;
        } else {
            // 数值型
            try {
                if (val.contains(".")) return Double.parseDouble(val);
                return Long.parseLong(val);
            } catch (NumberFormatException e) {
                return val;
            }
        }
    }

    /**
     * <AUTHOR>
     * @date 2025/8/13 17:07:46
     * @desc 解析值，根据值来确定ES的查询方式
     */
    private static ParsedValue parseValue(Object rawValue) {
        ParsedValue parsed = new ParsedValue();
        if (rawValue instanceof String) {
            String str = rawValue.toString().trim();

            if ("@".equals(str)) {
                parsed.isExists = true;
                return parsed;
            } else if ("!@".equals(str)) {
                parsed.isExists = true;
                parsed.isNot = true;
                return parsed;
            }
            // NOT + OR
            if (str.startsWith("!|")) {
                parsed.isNot = true;
                parsed.isOr = true;
                str = str.substring(2).trim();
            } else if (str.startsWith("|")) {
                parsed.isOr = true;
                str = str.substring(1).trim();
            } else if (str.startsWith("!")) {
                parsed.isNot = true;
                str = str.substring(1).trim();
            } else if (str.startsWith("~!")) {
                parsed.isMatch = true;
                parsed.isNot = true;
                str = str.substring(2).trim();
            } else if (str.startsWith("~")) {
                parsed.isMatch = true;
                str = str.substring(1).trim();
            }

            // OR 分组 id
            if (parsed.isOr && str.contains(":")) {
                String[] parts = str.split(":", 2);
                parsed.orGroupId = parts[0].trim();
                str = parts[1].trim();
            }

            // range 解析
            // 格式: "r:"作为前缀，后接：>100, >=100, <100, <=100, 100..200, 100..<200
            String rangeRegex = "^r:(>=|<=|>|<)?\\s*([^.<>=]+)\\s*(\\.\\.<?\\s*([^.<>=]+))?$";
            Matcher m = Pattern.compile(rangeRegex).matcher(str);
            if (m.matches()) {
                String op1 = m.group(1);
                String val1 = m.group(2);
                String dots = m.group(3);
                String val2 = m.group(4);

                // 对于 "r:xxx" 也会匹配到group(2)，这种情况，不处理，之后跳出，进行后续处理
                if (StrUtil.isNotBlank(val1) && StrUtil.isBlank(val2) && StrUtil.isBlank(op1) && StrUtil.isBlank(dots)) {
                } else {
                    parsed.isRange = true;

                    if (op1 != null) {
                        if (op1.equals(">")) {
                            parsed.rangeFrom = val1;
                            parsed.includeFrom = false;
                        } else if (op1.equals(">=")) {
                            parsed.rangeFrom = val1;
                            parsed.includeFrom = true;
                        } else if (op1.equals("<")) {
                            parsed.rangeTo = val1;
                            parsed.includeTo = false;
                        } else if (op1.equals("<=")) {
                            parsed.rangeTo = val1;
                            parsed.includeTo = true;
                        }
                    }

                    if (dots != null) {
                        parsed.rangeFrom = val1;
                        parsed.rangeTo = val2;
                        parsed.includeFrom = true;
                        parsed.includeTo = !dots.contains("<");
                    }
                    return parsed;
                }
            }

            parsed.values = Arrays.stream(str.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());
        } else if (rawValue instanceof Collection<?>) {
            parsed.values = new ArrayList<>((Collection<?>) rawValue);

        } else {
            parsed.values = Collections.singletonList(rawValue);
        }
        return parsed;
    }

    /**
     * <AUTHOR>
     * @date 2025/8/13 17:25:39
     * @desc 构建单个字段查询
     */
    private static QueryBuilder buildQuery(String esFieldName, ParsedValue parsed, Field field,
                                           Object value, Map<String, RangeQueryBuilder> rangeMap) {

        String fieldName = field.getName();
        boolean useMatch = field.isAnnotationPresent(EsMatch.class);
        boolean forceNumeric = field.isAnnotationPresent(EsNumeric.class);
        Class<?> numericType = forceNumeric ? field.getAnnotation(EsNumeric.class).type() : null;
        EsDateFormat dateFormat = field.getAnnotation(EsDateFormat.class);
        boolean isDate = dateFormat != null;
        Class<?> fieldType = field.getType();

        //范围方式单独处理
        //范围值方式优先于注解方式
        if (parsed.isRange) {
            return buildRangeFromValue(esFieldName, parsed, field);
        } else if (field.isAnnotationPresent(EsRange.class)) {
            if (CollUtil.isEmpty(parsed.values)) {
                return null;
            }
            return buildRangeOrTerms(esFieldName, parsed.values, field);
        } else {
            // String类型,暂时排除日期格式的字符串，下面单独处理,对于多值查询，优先使用逗号分割的方式，通过注解来确定ES字段类型
            if (fieldType == String.class && !((String) parsed.values.get(0)).matches("\\d{4}-\\d{2}-\\d{2}.*")) {
                if (useMatch || parsed.isMatch) {
                    return QueryBuilders.matchPhraseQuery(esFieldName, parsed.values.get(0).toString().trim());
                } else {
                    List<Object> convertedValues = parsed.values;
                    if (forceNumeric) convertedValues = convertListToNumeric(parsed.values, numericType);
                    //对于标记为数值类型的字段值为如下格式，xxx,xxx，则认为是区间查询
                    //对于数值类型确实要terms查询xxx,xxx，则暂时考虑再增加一个当前不存在的数值，比如负值，格式：xxx,xxx,-1
                    if (forceNumeric && convertedValues.size() == 2){
                        return QueryBuilders.rangeQuery(esFieldName).gte(convertedValues.get(0)).lte(convertedValues.get(1));
                    }
                    return QueryBuilders.termsQuery(esFieldName, convertedValues);
                }
            } else if (value instanceof Collection || value.getClass().isArray()) {
                List<Object> list = toList(value);
                if (forceNumeric) list = convertListToNumeric(list, numericType);
                if (!list.isEmpty()) {
                    return QueryBuilders.termsQuery(esFieldName, list);
                }
            }
            // 数值类型
            else if (Number.class.isAssignableFrom(fieldType)) {
                return  QueryBuilders.termsQuery(esFieldName, value);
            }
            // 日期范围处理
            else if (Date.class.isAssignableFrom(fieldType)
                    || fieldType == LocalDate.class
                    || fieldType == LocalDateTime.class
                    || (fieldType == String.class && ((String) value).matches("\\d{4}-\\d{2}-\\d{2}.*"))) {
                Object formattedDate = value;

                if (fieldType == String.class) {
                    String strVal = value.toString();
                    String[] parts = strVal.split(",");
                    if (parts.length == 2) {
                        RangeQueryBuilder range = QueryBuilders.rangeQuery(esFieldName);
                        if (!parts[0].trim().isEmpty())
                            range.gte(convertRangeValue(parts[0].trim(), isDate, true, true));
                        if (!parts[1].trim().isEmpty())
                            range.lte(convertRangeValue(parts[1].trim(), isDate, true, false));
                        if (isDate) range.format(dateFormat.value());
                        return range;
                    }
                } else if (fieldType == LocalDate.class) {
                    formattedDate = ((LocalDate) value).toString(); // 格式为 yyyy-MM-dd
                } else if (fieldType == LocalDateTime.class) {
                    // 将 LocalDateTime 转为字符串，格式为 Elasticsearch 所支持的时间格式（比如 "yyyy-MM-dd'T'HH:mm:ss"）
                    formattedDate = ((LocalDateTime) value).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                }
                if (fieldName.endsWith("Start") || fieldName.endsWith("End")) {
                    String baseEsField = esFieldName;
                    RangeQueryBuilder range = rangeMap.computeIfAbsent(baseEsField, QueryBuilders::rangeQuery);

                    if (fieldName.endsWith("Start")) {
                        range.gte(formattedDate);
                    } else {
                        range.lte(formattedDate);
                    }
                    rangeMap.put(baseEsField, range);
                } else {
                    return QueryBuilders.rangeQuery(esFieldName).gte(formattedDate).lte(formattedDate);
                }
            }
            // TODO: 可扩展数组、集合、布尔类型、Nested 等
            else {
                return QueryBuilders.termsQuery(esFieldName, value);
            }
            return QueryBuilders.termsQuery(fieldName, parsed.values);
        }
    }

    /**
     * <AUTHOR>
     * @date 2025/8/13 17:09:53
     * @desc 采用Range注解方式的处理
     */
    private static QueryBuilder buildRangeOrTerms(String fieldName, List<Object> values, Field field) {
        if (values.size() == 1) {
            return QueryBuilders.termsQuery(fieldName, values);
        }
        EsDateFormat dateFormat = field.getAnnotation(EsDateFormat.class);
        boolean isDate = dateFormat != null;
        RangeQueryBuilder range = QueryBuilders.rangeQuery(fieldName);
        if (!values.get(0).toString().isEmpty()) {
            range.gte(convertRangeValue(values.get(0).toString().trim(), isDate, true, true));
        }
        if (values.size() > 1 && !values.get(1).toString().isEmpty()) {
            range.lte(convertRangeValue(values.get(1).toString().trim(), isDate, true, false));
        }
        return range;
    }

    /**
     * <AUTHOR>
     * @date 2025/8/13 17:12:55
     * @desc 采用范围值格式的方式来处理Range查询
     * 格式: r:>100, r:>=100, r:<100, r:<=100, r:100..200, r:100..<200
     * 格式: r:>2025-08-13 00:00:00, r:>=2025-08-13, r:<2025-08-13, r:<=2025-08-13,
     * r:2024-08-13..2025-08-13, r:2025-08-13 00:00:00..<2025-08-13 23:59:59
     */
    private static QueryBuilder buildRangeFromValue(String fieldName, ParsedValue pv, Field field) {
        EsDateFormat dateFormat = field.getAnnotation(EsDateFormat.class);
        boolean isDate = dateFormat != null;

        RangeQueryBuilder range = QueryBuilders.rangeQuery(fieldName);
        if (pv.rangeFrom != null) {
            if(pv.rangeFrom.toString().matches("\\d{4}-\\d{2}-\\d{2}.*")) isDate = true;
            if (isDate) {
                //日期和时间类型的话，要转换下
                if (pv.includeFrom) {
                    range.gte(convertRangeValue(pv.rangeFrom.toString(), isDate, true, true));
                } else {
                    range.gt(convertRangeValue(pv.rangeFrom.toString(), isDate, true, true));
                }
            } else {
                if (pv.includeFrom) range.gte(pv.rangeFrom);
                else range.gt(pv.rangeFrom);
            }
        }
        if (pv.rangeTo != null) {
            if(pv.rangeTo.toString().matches("\\d{4}-\\d{2}-\\d{2}.*")) isDate = true;
            if (isDate) {
                //日期和时间类型的话，要转换时间格式
                if (pv.includeTo) {
                    range.lte(convertRangeValue(pv.rangeTo.toString(), isDate, true, false));
                } else {
                    range.lt(convertRangeValue(pv.rangeTo.toString(), isDate, true, false));
                }
            } else {
                if (pv.includeTo) range.lte(pv.rangeTo);
                else range.lt(pv.rangeTo);
            }
        }
        return range;
    }

    /** 排序 */
    private static void applySort(SearchSourceBuilder sourceBuilder, Object dto) {
        try {
            Field orderByField = dto.getClass().getDeclaredField("orderBy");
            Field ascFlagField = dto.getClass().getDeclaredField("ascFlag");
            orderByField.setAccessible(true);
            ascFlagField.setAccessible(true);
            String orderBy = (String) orderByField.get(dto);
            boolean ascFlag = ascFlagField.getBoolean(dto);

            if (orderBy == null || orderBy.trim().isEmpty()) return;

            String[] fields = orderBy.split(",");
            for (String fieldSpec : fields) {
                fieldSpec = fieldSpec.trim();
                if (fieldSpec.isEmpty()) continue;

                String fieldName = fieldSpec;
                SortOrder sortOrder = ascFlag ? SortOrder.ASC : SortOrder.DESC;

                if (fieldSpec.contains(":")) {
                    String[] parts = fieldSpec.split(":");
                    fieldName = parts[0].trim();
                    if (parts.length > 1) {
                        String dir = parts[1].trim().toLowerCase();
                        if ("asc".equals(dir)) sortOrder = SortOrder.ASC;
                        else if ("desc".equals(dir)) sortOrder = SortOrder.DESC;
                    }
                }

                if (!fieldName.isEmpty()) {
                    sourceBuilder.sort(fieldName, sortOrder);
                }
            }
        } catch (NoSuchFieldException | IllegalAccessException ignored) {}
    }
}
